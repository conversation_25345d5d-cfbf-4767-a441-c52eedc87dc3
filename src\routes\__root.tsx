import RootLayout from "@/components/layout/RootLayout";
import { type AuthState } from "@/features/auth/types";
import { createRootRouteWithContext, Link } from "@tanstack/react-router";

type MyRouterContext = {
	auth: AuthState;
};

// Custom NotFound component
const NotFound = () => {
	return (
		<div className="min-h-screen flex flex-col items-center justify-center bg-white">
			<div className="text-center p-8 max-w-md">
				<h1 className="text-4xl font-bold text-accent mb-4">
					Page Not Found
				</h1>
				<p className="text-[#64748B] mb-8">
					The page you are looking for doesn't exist or has been moved.
				</p>
				<Link
					to="/dashboard"
					className="bg-accent text-white px-6 py-3 rounded-lg font-medium hover:bg-opacity-90 transition-colors"
				>
					Back to Home
				</Link>
			</div>
		</div>
	);
};

export const Route = createRootRouteWithContext<MyRouterContext>()({
	component: RootLayout,
	notFoundComponent: NotFound,
});
